# Email Verification Configuration Example
# Copy this file to .env and update with your actual values

# =============================================================================
# EMAIL SERVICE CONFIGURATION
# =============================================================================

# Email service provider: smtp, sendgrid, or ses
EMAIL_SERVICE_PROVIDER=smtp

# From email configuration
EMAIL_FROM_ADDRESS=<EMAIL>
EMAIL_FROM_NAME=Dhruva Platform

# Frontend URLs
FRONTEND_BASE_URL=http://localhost:3000
EMAIL_VERIFICATION_URL=${FRONTEND_BASE_URL}/verify-email

# =============================================================================
# SMTP CONFIGURATION (for EMAIL_SERVICE_PROVIDER=smtp)
# =============================================================================

# Gmail SMTP Configuration
SMTP_SERVER=smtp.gmail.com
SMTP_PORT=587
SMTP_USERNAME=<EMAIL>
SMTP_PASSWORD=your-16-character-app-password
SMTP_USE_TLS=true

# Outlook SMTP Configuration (alternative)
# SMTP_SERVER=smtp-mail.outlook.com
# SMTP_PORT=587
# SMTP_USERNAME=<EMAIL>
# SMTP_PASSWORD=your-outlook-password
# SMTP_USE_TLS=true

# =============================================================================
# SENDGRID CONFIGURATION (for EMAIL_SERVICE_PROVIDER=sendgrid)
# =============================================================================

# SendGrid API Key
# SENDGRID_API_KEY=SG.your-sendgrid-api-key-here

# SendGrid Template IDs (optional, for advanced templating)
# EMAIL_VERIFICATION_TEMPLATE_ID=d-1234567890abcdef
# EMAIL_WELCOME_TEMPLATE_ID=d-fedcba0987654321

# =============================================================================
# AMAZON SES CONFIGURATION (for EMAIL_SERVICE_PROVIDER=ses)
# =============================================================================

# AWS Credentials
# AWS_ACCESS_KEY_ID=your-aws-access-key
# AWS_SECRET_ACCESS_KEY=your-aws-secret-key
# AWS_REGION=us-east-1

# =============================================================================
# EMAIL VERIFICATION SETTINGS
# =============================================================================

# Token expiry time in hours (default: 24)
EMAIL_VERIFICATION_TOKEN_EXPIRY_HOURS=24

# Maximum verification attempts per token (default: 5)
EMAIL_VERIFICATION_MAX_ATTEMPTS=5

# =============================================================================
# RATE LIMITING SETTINGS
# =============================================================================

# Signup requests per IP per hour
RATE_LIMIT_SIGNUP_PER_IP_PER_HOUR=5

# Verification attempts per IP per hour
RATE_LIMIT_VERIFY_PER_IP_PER_HOUR=10

# Resend verification requests per email per hour
RATE_LIMIT_RESEND_PER_EMAIL_PER_HOUR=3

# Registration status checks per IP per hour
RATE_LIMIT_STATUS_PER_IP_PER_HOUR=20

# =============================================================================
# EXISTING DHRUVA PLATFORM CONFIGURATION
# Keep your existing configuration below this line
# =============================================================================

# Database Configuration
MONGO_APP_DB_USERNAME=dhruvaadmin
MONGO_APP_DB_PASSWORD=dhruva123
MONGO_LOG_DB_USERNAME=dhruvalogadmin
MONGO_LOG_DB_PASSWORD=dhruvalog123
APP_DB_NAME=admin
APP_DB_CONNECTION_STRING=mongodb://${MONGO_APP_DB_USERNAME}:${MONGO_APP_DB_PASSWORD}@dhruva-platform-app-db:27017/admin?authSource=admin

# Redis Configuration
REDIS_HOST=dhruva-platform-redis
REDIS_PORT=6379
REDIS_PASSWORD=dhruva123
REDIS_DB=0
REDIS_SECURE=false

# TimescaleDB Configuration
TIMESCALE_USER=dhruva
TIMESCALE_PASSWORD=dhruva123
TIMESCALE_DATABASE_NAME=dhruva_metering
TIMESCALE_PORT=5432
TIMESCALE_HOST=dhruva-platform-timescaledb

# RabbitMQ Configuration
RABBITMQ_DEFAULT_USER=admin
RABBITMQ_DEFAULT_PASS=admin123
RABBITMQ_DEFAULT_VHOST=dhruva_host
CELERY_BROKER_URL=amqp://${RABBITMQ_DEFAULT_USER}:${RABBITMQ_DEFAULT_PASS}@dhruva-platform-rabbitmq:5672/${RABBITMQ_DEFAULT_VHOST}

# JWT Configuration
JWT_SECRET_KEY=your-jwt-secret-key-here

# Environment
ENVIRONMENT=development

# Usage Email Configuration (existing)
USAGE_EMAIL_RECIPIENTS=<EMAIL>
USAGE_EMAIL_SENDER=<EMAIL>
SMTP_USERNAME_USAGE=<EMAIL>
SMTP_PASSWORD_USAGE=your-usage-app-password

# Cloud Storage (if using)
# AZURE_STORAGE_CONNECTION_STRING=your-azure-connection-string
# AWS_S3_BUCKET_NAME=your-s3-bucket-name

# =============================================================================
# DEVELOPMENT/TESTING NOTES
# =============================================================================

# For development with Gmail SMTP:
# 1. Enable 2-Factor Authentication on your Google account
# 2. Generate an App Password: https://myaccount.google.com/apppasswords
# 3. Use the 16-character app password (not your regular password)
# 4. Set SMTP_USERNAME to your Gmail address
# 5. Set SMTP_PASSWORD to the app password

# For production:
# 1. Consider using SendGrid or Amazon SES for better deliverability
# 2. Set up proper domain authentication (SPF, DKIM records)
# 3. Use environment variable management (AWS Secrets Manager, etc.)
# 4. Monitor email delivery rates and bounce rates

# Testing the email service:
# 1. Start the services: docker compose up -d
# 2. Test signup: curl -X POST "http://localhost:8000/auth/signup" -H "Content-Type: application/json" -d '{"name": "Test User", "email": "<EMAIL>", "password": "testpass123"}'
# 3. Check your email for verification link
# 4. Test verification: curl -X GET "http://localhost:8000/auth/verify-email?token=YOUR_TOKEN"
