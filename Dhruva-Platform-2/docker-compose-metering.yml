services:
  rabbitmq_server:
    image: rabbitmq:3-management
    container_name: dhruva-platform-rabbitmq
    platform: linux/amd64
    hostname: rabbithost
    ports:
      - "15672:15672"
      - "5672:5672"
    volumes:
      - ./server/celery_backend/rabbitmq.conf:/etc/rabbitmq/rabbitmq.conf:ro
      - ./server/celery_backend/definitions.json:/etc/rabbitmq/definitions.json:ro
    env_file:
      - .env
    networks:
      - dhruva-network
    healthcheck:      
      test: ["CMD-SHELL", "rabbitmq-diagnostics -q check_port_connectivity || exit 1"]
      interval: 10s
      timeout: 5s
      retries: 12
      start_period: 10s


  celery-metering:
    container_name: celery-metering
    platform: linux/amd64
    build:
      context: ./server
    working_dir: /src
    depends_on:
      rabbitmq_server:
        condition: service_started
      timescaledb:
        condition: service_healthy
    volumes:
      - ./server:/src
    env_file:
      - .env
    command: sh -c "python3 -m celery -A celery_backend.celery_app worker -Q data-log,heartbeat,upload-feedback-dump,send-usage-email"
    networks:
      - dhruva-network

  celery-monitoring:
    container_name: celery-monitoring
    platform: linux/amd64
    build:
      context: ./server
      dockerfile: Dockerfile
    working_dir: /src
    depends_on:
      rabbitmq_server:
        condition: service_started
      timescaledb:
        condition: service_healthy
    volumes:
      - ./server:/src
    env_file:
      - .env
    command: sh -c "python3 -m celery -A celery_backend.celery_app worker -Q metrics-log"
    networks:
      - dhruva-network

  celery_beat:
    container_name: celery_beat
    platform: linux/amd64
    build:
      context: ./server
      dockerfile: Dockerfile
    working_dir: /src
    depends_on:
      rabbitmq_server:
        condition: service_started
      celery-metering:
        condition: service_started
      timescaledb:
        condition: service_healthy
    volumes:
      - ./server:/src
    env_file:
      - .env
    command: sh -c "python3 -m celery -A celery_backend.celery_app beat --loglevel DEBUG"
    networks:
      - dhruva-network

  timescaledb:
    container_name: timescaledb
    image: "timescale/timescaledb:latest-pg15"
    ports:
      - "5432:5432"
    platform: linux/amd64
    volumes:
      - timescaledb:/home/<USER>/pgdata/data
    environment:
      - POSTGRES_PASSWORD=$TIMESCALE_PASSWORD
      - POSTGRES_DB=$TIMESCALE_DATABASE_NAME
      - POSTGRES_USER=$TIMESCALE_USER
    env_file:
      - .env
    networks:
      - dhruva-network

volumes:
  timescaledb: {}

networks:
  dhruva-network:
    name: dhruva-network
