# Email Verification Configuration for Testing
# Copy these variables to your main .env file

# Email Service Configuration
EMAIL_SERVICE_PROVIDER=smtp
EMAIL_FROM_ADDRESS=<EMAIL>
EMAIL_FROM_NAME=Dhruva Platform

# Frontend URLs
FRONTEND_BASE_URL=http://localhost:3000
EMAIL_VERIFICATION_URL=${FRONTEND_BASE_URL}/verify-email

# SMTP Configuration (for development testing)
# NOTE: Replace with your actual SMTP credentials
SMTP_SERVER=smtp.gmail.com
SMTP_PORT=587
SMTP_USERNAME=<EMAIL>
SMTP_PASSWORD=your-16-character-app-password
SMTP_USE_TLS=true

# Email Verification Settings
EMAIL_VERIFICATION_TOKEN_EXPIRY_HOURS=24
EMAIL_VERIFICATION_MAX_ATTEMPTS=5

# Rate Limiting Settings
RATE_LIMIT_SIGNUP_PER_IP_PER_HOUR=5
RATE_LIMIT_VERIFY_PER_IP_PER_HOUR=10
RATE_LIMIT_RESEND_PER_EMAIL_PER_HOUR=3
RATE_LIMIT_STATUS_PER_IP_PER_HOUR=20
