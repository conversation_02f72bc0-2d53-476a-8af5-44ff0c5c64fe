/* ASR Tester Component Styles */

.asr-tester {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Robot<PERSON>', 'Oxygen',
    'Ubuntu', '<PERSON><PERSON><PERSON>', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  line-height: 1.6;
}

.asr-header {
  text-align: center;
  margin-bottom: 30px;
  padding: 20px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border-radius: 10px;
}

.asr-header h1 {
  margin: 0 0 10px 0;
  font-size: 2.5rem;
  font-weight: 700;
}

.asr-header p {
  margin: 0;
  font-size: 1.1rem;
  opacity: 0.9;
}

.error-banner {
  background-color: #fee;
  border: 1px solid #fcc;
  color: #c33;
  padding: 15px;
  border-radius: 5px;
  margin-bottom: 20px;
  text-align: center;
  font-weight: 500;
}

.asr-content {
  display: grid;
  grid-template-columns: 1fr;
  gap: 20px;
}

/* Panel Styles */
.config-panel,
.microphone-panel,
.upload-panel,
.results-panel,
.logs-panel {
  background: white;
  border: 1px solid #e1e5e9;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.config-panel h2,
.microphone-panel h2,
.upload-panel h2,
.results-panel h2,
.logs-panel h2 {
  margin: 0 0 20px 0;
  color: #2c3e50;
  font-size: 1.5rem;
  font-weight: 600;
  border-bottom: 2px solid #3498db;
  padding-bottom: 10px;
}

/* Configuration Panel */
.config-group {
  margin-bottom: 15px;
}

.config-group label {
  display: block;
  margin-bottom: 5px;
  font-weight: 500;
  color: #34495e;
}

.config-group select,
.config-group input {
  width: 100%;
  padding: 10px;
  border: 1px solid #bdc3c7;
  border-radius: 4px;
  font-size: 14px;
  transition: border-color 0.3s ease;
}

.config-group select:focus,
.config-group input:focus {
  outline: none;
  border-color: #3498db;
  box-shadow: 0 0 0 2px rgba(52, 152, 219, 0.2);
}

.config-group select:disabled,
.config-group input:disabled {
  background-color: #ecf0f1;
  cursor: not-allowed;
}

/* Button Styles */
.btn {
  padding: 10px 20px;
  border: none;
  border-radius: 4px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  margin-right: 10px;
  margin-bottom: 10px;
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.btn-primary {
  background-color: #3498db;
  color: white;
}

.btn-primary:hover:not(:disabled) {
  background-color: #2980b9;
}

.btn-secondary {
  background-color: #95a5a6;
  color: white;
}

.btn-secondary:hover:not(:disabled) {
  background-color: #7f8c8d;
}

.btn-success {
  background-color: #27ae60;
  color: white;
}

.btn-success:hover:not(:disabled) {
  background-color: #229954;
}

.btn-danger {
  background-color: #e74c3c;
  color: white;
}

.btn-danger:hover:not(:disabled) {
  background-color: #c0392b;
}

.btn-small {
  padding: 5px 10px;
  font-size: 12px;
}

/* Recording Controls */
.recording-controls {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
  margin-bottom: 20px;
}

.recording-status {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 15px;
  margin-bottom: 20px;
}

.status-indicator {
  padding: 10px;
  border-radius: 4px;
  font-weight: 500;
  text-align: center;
}

.status-indicator.idle {
  background-color: #ecf0f1;
  color: #2c3e50;
}

.status-indicator.recording {
  background-color: #e74c3c;
  color: white;
  animation: pulse 1s infinite;
}

.status-indicator.processing {
  background-color: #f39c12;
  color: white;
}

.status-indicator.completed {
  background-color: #27ae60;
  color: white;
}

.status-indicator.error {
  background-color: #e74c3c;
  color: white;
}

@keyframes pulse {
  0% { opacity: 1; }
  50% { opacity: 0.7; }
  100% { opacity: 1; }
}

.recording-timer {
  padding: 10px;
  background-color: #3498db;
  color: white;
  border-radius: 4px;
  text-align: center;
  font-weight: 500;
  font-family: 'Courier New', monospace;
}

.permission-status {
  padding: 10px;
  border-radius: 4px;
  text-align: center;
  font-weight: 500;
}

.permission-status.granted {
  background-color: #d5f4e6;
  color: #27ae60;
}

.permission-status.denied {
  background-color: #fadbd8;
  color: #e74c3c;
}

/* File Upload */
.file-input {
  display: none;
}

.upload-controls {
  margin-bottom: 20px;
}

.file-info,
.recording-result {
  background-color: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 4px;
  padding: 15px;
}

.file-details,
.result-details {
  margin-top: 10px;
}

.file-details p,
.result-details p {
  margin: 5px 0;
  color: #6c757d;
}

/* Results Panel */
.processing-indicator {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 20px;
  background-color: #f8f9fa;
  border-radius: 4px;
  margin-bottom: 20px;
}

.spinner {
  width: 20px;
  height: 20px;
  border: 2px solid #f3f3f3;
  border-top: 2px solid #3498db;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.error-message {
  background-color: #fadbd8;
  border: 1px solid #f5b7b1;
  color: #e74c3c;
  padding: 15px;
  border-radius: 4px;
  margin-bottom: 20px;
}

.transcription-result {
  background-color: #d5f4e6;
  border: 1px solid #a9dfbf;
  border-radius: 4px;
  padding: 15px;
}

.transcription-text {
  background-color: white;
  border: 1px solid #bdc3c7;
  border-radius: 4px;
  padding: 15px;
  margin: 10px 0;
  font-family: 'Georgia', serif;
  font-size: 16px;
  line-height: 1.6;
  min-height: 60px;
  white-space: pre-wrap;
  word-wrap: break-word;
}

/* Logs Panel */
.logs-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.logs-container {
  background-color: #2c3e50;
  border-radius: 4px;
  padding: 15px;
  max-height: 300px;
  overflow-y: auto;
}

.logs-content {
  color: #ecf0f1;
  font-family: 'Courier New', monospace;
  font-size: 12px;
  line-height: 1.4;
  margin: 0;
  white-space: pre-wrap;
  word-wrap: break-word;
}

.no-logs {
  color: #95a5a6;
  font-style: italic;
  text-align: center;
  margin: 20px 0;
}

/* Responsive Design */
@media (min-width: 768px) {
  .asr-content {
    grid-template-columns: 1fr 1fr;
  }
  
  .config-panel {
    grid-column: 1 / -1;
  }
  
  .logs-panel {
    grid-column: 1 / -1;
  }
}

@media (min-width: 1024px) {
  .recording-controls {
    justify-content: flex-start;
  }
  
  .upload-controls {
    display: flex;
    align-items: center;
    gap: 15px;
  }
}
