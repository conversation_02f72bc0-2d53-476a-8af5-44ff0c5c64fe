/**
 * Streaming ASR API Utilities for Dhruva Platform
 * Implements Socket.IO client for real-time ASR streaming
 */

import { io, Socket } from 'socket.io-client';

// Streaming endpoints in order of preference
const STREAMING_ENDPOINTS = [
  'http://localhost:8000/socket.io',
  'http://localhost:8000/socket_asr.io',
  'http://*************:8000/socket.io',
  'http://*************:8000/socket_asr.io'
];

export interface StreamingConfig {
  serviceId: string;
  language: string;
  samplingRate: number;
  audioFormat: string;
  encoding: string;
  apiKey: string;
  responseFrequencyMs?: number;
  bufferSize?: number;
}

export interface StreamingStatus {
  state: 'disconnected' | 'connecting' | 'connected' | 'streaming' | 'processing' | 'error';
  message?: string;
  error?: string;
  duration?: number;
}

export interface StreamingResult {
  transcript: string;
  isFinal: boolean;
  confidence?: number;
  timestamp: number;
}

export type StreamingStatusCallback = (status: StreamingStatus) => void;
export type StreamingResultCallback = (result: StreamingResult) => void;

export type StreamerType = 'modern' | 'legacy';

/**
 * Detect streamer type based on endpoint URL
 */
function detectStreamerType(endpoint: string): StreamerType {
  return endpoint.includes('/socket_asr.io') ? 'legacy' : 'modern';
}

export class StreamingASRClient {
  private socket: Socket | null = null;
  private config: StreamingConfig | null = null;
  private statusCallback?: StreamingStatusCallback;
  private resultCallback?: StreamingResultCallback;
  private currentEndpoint: string | null = null;
  private currentStreamerType: StreamerType | null = null;
  private isStreaming: boolean = false;
  private startTime: number = 0;
  private audioBuffer: ArrayBuffer[] = [];
  private reconnectAttempts: number = 0;
  private maxReconnectAttempts: number = 3;

  constructor(
    statusCallback?: StreamingStatusCallback,
    resultCallback?: StreamingResultCallback
  ) {
    this.statusCallback = statusCallback;
    this.resultCallback = resultCallback;
  }

  /**
   * Test connectivity to streaming endpoints
   */
  async testEndpoints(): Promise<string[]> {
    const workingEndpoints: string[] = [];
    
    for (const endpoint of STREAMING_ENDPOINTS) {
      try {
        console.log(`[StreamingASR] Testing endpoint: ${endpoint}`);
        
        const streamerType = detectStreamerType(endpoint);
        const testSocket = io(endpoint, {
          timeout: 5000,
          transports: ['websocket', 'polling'],
          // Use minimal auth for testing
          auth: streamerType === 'modern' ? { apiKey: 'test' } : undefined
        });

        const isWorking = await new Promise<boolean>((resolve) => {
          const timeout = setTimeout(() => {
            console.log(`[StreamingASR] Endpoint ${endpoint} test timeout`);
            testSocket.disconnect();
            resolve(false);
          }, 5000);

          testSocket.on('connect', () => {
            console.log(`[StreamingASR] Endpoint ${endpoint} connected successfully`);
            clearTimeout(timeout);
            testSocket.disconnect();
            resolve(true);
          });

          testSocket.on('connect_error', (error) => {
            console.log(`[StreamingASR] Endpoint ${endpoint} connection error:`, error.message || (error as any).description || error);
            clearTimeout(timeout);
            testSocket.disconnect();
            resolve(false);
          });

          testSocket.on('abort', (data) => {
            console.log(`[StreamingASR] Endpoint ${endpoint} authentication failed:`, data);
            clearTimeout(timeout);
            testSocket.disconnect();
            resolve(false);
          });
        });

        if (isWorking) {
          workingEndpoints.push(endpoint);
          console.log(`[StreamingASR] Endpoint ${endpoint} is working`);
        } else {
          console.log(`[StreamingASR] Endpoint ${endpoint} is not accessible`);
        }
      } catch (error) {
        console.log(`[StreamingASR] Error testing endpoint ${endpoint}:`, error);
      }
    }

    return workingEndpoints;
  }

  /**
   * Connect to streaming server
   */
  async connect(config: StreamingConfig): Promise<void> {
    try {
      this.config = config;
      this.updateStatus('connecting', 'Testing endpoints and establishing connection...');

      // Test endpoints to find working one
      const workingEndpoints = await this.testEndpoints();
      
      if (workingEndpoints.length === 0) {
        throw new Error('No working streaming endpoints found. Please ensure the Dhruva Platform server is running.');
      }

      this.currentEndpoint = workingEndpoints[0];
      this.currentStreamerType = detectStreamerType(this.currentEndpoint);
      console.log(`[StreamingASR] Using endpoint: ${this.currentEndpoint} (${this.currentStreamerType} streamer)`);

      // Create socket connection with proper authentication
      const socketConfig: any = {
        timeout: 10000,
        transports: ['websocket', 'polling']
      };

      // Modern streamer requires authentication via auth parameter
      if (this.currentStreamerType === 'modern') {
        socketConfig.auth = {
          apiKey: config.apiKey,
          serviceId: config.serviceId,
          language: config.language,
          samplingRate: config.samplingRate.toString(),
          audioFormat: config.audioFormat,
          encoding: config.encoding
        };
        console.log(`[StreamingASR] Using auth-based authentication for modern streamer`);
      } else {
        // Legacy streamer uses query parameters
        socketConfig.query = {
          serviceId: config.serviceId,
          language: config.language,
          samplingRate: config.samplingRate.toString(),
          apiKey: config.apiKey,
          audioFormat: config.audioFormat,
          encoding: config.encoding
        };
        console.log(`[StreamingASR] Using query-based authentication for legacy streamer`);
      }

      this.socket = io(this.currentEndpoint, socketConfig);

      this.setupSocketHandlers();

      // Wait for connection
      return new Promise((resolve, reject) => {
        const timeout = setTimeout(() => {
          reject(new Error('Connection timeout'));
        }, 10000);

        this.socket!.on('connect', () => {
          clearTimeout(timeout);
          console.log('[StreamingASR] Connected to streaming server');
          this.updateStatus('connected', 'Connected to streaming server');
          this.reconnectAttempts = 0;
          resolve();
        });

        this.socket!.on('connect_error', (error) => {
          clearTimeout(timeout);
          console.error('[StreamingASR] Connection error:', error);
          reject(new Error(`Connection failed: ${error.message}`));
        });
      });
    } catch (error: any) {
      console.error('[StreamingASR] Connect error:', error);
      this.updateStatus('error', undefined, error.message);
      throw error;
    }
  }

  /**
   * Setup socket event handlers
   */
  private setupSocketHandlers(): void {
    if (!this.socket) return;

    this.socket.on('connect', () => {
      console.log('[StreamingASR] Socket connected');
      this.updateStatus('connected', 'Connected to streaming server');
    });

    this.socket.on('disconnect', (reason) => {
      console.log('[StreamingASR] Socket disconnected:', reason);
      this.isStreaming = false;
      
      if (reason === 'io server disconnect') {
        this.updateStatus('disconnected', 'Disconnected by server');
      } else {
        this.updateStatus('disconnected', 'Connection lost');
        this.attemptReconnect();
      }
    });

    this.socket.on('connect_error', (error) => {
      console.error('[StreamingASR] Socket connection error:', error);
      const errorMessage = error.message || (error as any).description || error.toString();
      this.updateStatus('error', undefined, `Connection error: ${errorMessage}`);
    });

    // Handle authentication responses
    this.socket.on('abort', (data) => {
      console.error('[StreamingASR] Authentication failed:', data);
      this.updateStatus('error', undefined, `Authentication failed: ${data}`);
    });

    // Handle streaming responses for both streamer types
    this.socket.on('ready', () => {
      console.log('[StreamingASR] Server ready for streaming (modern streamer)');
      this.updateStatus('connected', 'Server ready for streaming');
    });

    this.socket.on('connect-success', () => {
      console.log('[StreamingASR] Stream connection successful (legacy streamer)');
      this.updateStatus('connected', 'Stream connection successful');
    });

    // Handle transcript responses for both streamer types
    this.socket.on('transcript', (data: any) => {
      console.log('[StreamingASR] Received transcript:', data);
      this.handleTranscriptResult(data);
    });

    this.socket.on('partial_transcript', (data: any) => {
      console.log('[StreamingASR] Received partial transcript:', data);
      this.handleTranscriptResult(data, false);
    });

    this.socket.on('final_transcript', (data: any) => {
      console.log('[StreamingASR] Received final transcript:', data);
      this.handleTranscriptResult(data, true);
    });

    // Modern streamer response event
    this.socket.on('response', (data: any, isFinal: boolean = false) => {
      console.log('[StreamingASR] Received response from modern streamer:', data, 'isFinal:', isFinal);
      this.handleTranscriptResult(data, isFinal);
    });

    this.socket.on('error', (error: any) => {
      console.error('[StreamingASR] Server error:', error);
      this.updateStatus('error', undefined, `Server error: ${error}`);
    });

    this.socket.on('terminate', () => {
      console.log('[StreamingASR] Stream terminated by server');
      this.stopStreaming();
    });
  }

  /**
   * Update status and notify callback
   */
  private updateStatus(state: StreamingStatus['state'], message?: string, error?: string): void {
    const status: StreamingStatus = {
      state,
      message,
      error,
      duration: this.startTime ? (Date.now() - this.startTime) / 1000 : 0
    };

    if (this.statusCallback) {
      this.statusCallback(status);
    }
  }

  /**
   * Handle transcript results from both streamer types
   */
  private handleTranscriptResult(data: any, isFinal: boolean = false): void {
    let transcript = '';
    let confidence: number | undefined;

    // Handle different response formats
    if (typeof data === 'string') {
      transcript = data;
    } else if (data && typeof data === 'object') {
      // Modern streamer response format
      if (data.output && Array.isArray(data.output)) {
        const output = data.output[0];
        if (output && output.target) {
          transcript = output.target;
          confidence = output.confidence;
        }
      }
      // Legacy streamer or simple formats
      else {
        transcript = data.transcript || data.text || data.result || data.output || '';
        confidence = data.confidence;
      }
    }

    if (transcript && this.resultCallback) {
      const result: StreamingResult = {
        transcript: transcript.trim(),
        isFinal,
        confidence,
        timestamp: Date.now()
      };

      console.log(`[StreamingASR] Processed transcript result:`, result);
      this.resultCallback(result);
    } else if (!transcript) {
      console.log(`[StreamingASR] No transcript found in response:`, data);
    }
  }

  /**
   * Attempt to reconnect
   */
  private attemptReconnect(): void {
    if (this.reconnectAttempts >= this.maxReconnectAttempts) {
      this.updateStatus('error', undefined, 'Maximum reconnection attempts reached');
      return;
    }

    this.reconnectAttempts++;
    const delay = Math.min(1000 * Math.pow(2, this.reconnectAttempts), 10000);

    console.log(`[StreamingASR] Attempting reconnection ${this.reconnectAttempts}/${this.maxReconnectAttempts} in ${delay}ms`);

    setTimeout(() => {
      if (this.config) {
        this.connect(this.config).catch((error) => {
          console.error('[StreamingASR] Reconnection failed:', error);
        });
      }
    }, delay);
  }

  /**
   * Start streaming audio
   */
  async startStreaming(): Promise<void> {
    if (!this.socket || !this.socket.connected) {
      throw new Error('Not connected to streaming server');
    }

    if (this.isStreaming) {
      throw new Error('Already streaming');
    }

    try {
      console.log('[StreamingASR] Starting audio stream...');
      this.isStreaming = true;
      this.startTime = Date.now();
      this.audioBuffer = [];

      this.updateStatus('streaming', 'Starting audio stream...');

      // Initialize streaming session based on streamer type
      if (this.currentStreamerType === 'legacy') {
        // Legacy ASR streamer - simple connection
        console.log('[StreamingASR] Initializing legacy ASR streamer...');
        this.socket.emit('connect_mic_stream');
      } else {
        // Modern generic task streamer - requires task sequence
        console.log('[StreamingASR] Initializing modern task streamer...');
        const taskSequence = {
          taskType: "asr",
          config: {
            serviceId: this.config!.serviceId,
            language: {
              sourceLanguage: this.config!.language
            },
            audioFormat: this.config!.audioFormat,
            samplingRate: this.config!.samplingRate,
            encoding: this.config!.encoding
          }
        };

        const streamingConfig = {
          response_frequency_ms: this.config!.responseFrequencyMs || 2000,
          buffer_size: this.config!.bufferSize || 4096
        };

        this.socket.emit('start', taskSequence, streamingConfig);
      }

      this.updateStatus('streaming', 'Audio streaming active');
      console.log('[StreamingASR] Audio streaming started');
    } catch (error: any) {
      this.isStreaming = false;
      console.error('[StreamingASR] Failed to start streaming:', error);
      this.updateStatus('error', undefined, `Failed to start streaming: ${error.message}`);
      throw error;
    }
  }

  /**
   * Send audio data to streaming server
   */
  sendAudioData(audioData: ArrayBuffer): void {
    if (!this.socket || !this.socket.connected || !this.isStreaming) {
      console.warn('[StreamingASR] Cannot send audio data: not streaming');
      return;
    }

    try {
      // Convert ArrayBuffer to base64 for transmission
      const uint8Array = new Uint8Array(audioData);
      const base64Data = this.arrayBufferToBase64(uint8Array);

      if (this.currentStreamerType === 'legacy') {
        // Legacy ASR streamer format: mic_data(base64_data, language_code, is_speaking, disconnect_stream)
        console.log(`[StreamingASR] Sending audio data to legacy streamer: ${audioData.byteLength} bytes`);
        this.socket.emit('mic_data', base64Data, this.config!.language, true, false);
      } else {
        // Modern task streamer format: data(input_data, streaming_config, clear_server_state, disconnect_stream)
        console.log(`[StreamingASR] Sending audio data to modern streamer: ${audioData.byteLength} bytes`);
        const inputData = {
          audio: [{
            audioContent: base64Data
          }]
        };

        const streamingConfig = {
          response_frequency_ms: this.config!.responseFrequencyMs || 2000
        };

        this.socket.emit('data', inputData, streamingConfig, false, false);
      }

      console.log(`[StreamingASR] Sent audio chunk: ${audioData.byteLength} bytes`);
    } catch (error) {
      console.error('[StreamingASR] Error sending audio data:', error);
      this.updateStatus('error', undefined, `Error sending audio: ${error}`);
    }
  }

  /**
   * Stop streaming
   */
  stopStreaming(): void {
    if (!this.isStreaming) {
      return;
    }

    try {
      console.log('[StreamingASR] Stopping audio stream...');
      this.isStreaming = false;

      if (this.socket && this.socket.connected) {
        if (this.currentStreamerType === 'legacy') {
          // Legacy ASR streamer: send empty data with disconnect flag
          console.log('[StreamingASR] Stopping legacy streamer...');
          this.socket.emit('mic_data', '', this.config!.language, false, true);
        } else {
          // Modern task streamer: send stop event
          console.log('[StreamingASR] Stopping modern streamer...');
          this.socket.emit('stop', true);
        }
      }

      this.updateStatus('processing', 'Processing final results...');
      console.log('[StreamingASR] Audio streaming stopped');
    } catch (error) {
      console.error('[StreamingASR] Error stopping stream:', error);
      this.updateStatus('error', undefined, `Error stopping stream: ${error}`);
    }
  }

  /**
   * Disconnect from streaming server
   */
  disconnect(): void {
    try {
      console.log('[StreamingASR] Disconnecting from streaming server...');

      if (this.isStreaming) {
        this.stopStreaming();
      }

      if (this.socket) {
        this.socket.disconnect();
        this.socket = null;
      }

      this.currentEndpoint = null;
      this.currentStreamerType = null;
      this.config = null;
      this.reconnectAttempts = 0;
      this.audioBuffer = [];

      this.updateStatus('disconnected', 'Disconnected from streaming server');
      console.log('[StreamingASR] Disconnected successfully');
    } catch (error) {
      console.error('[StreamingASR] Error during disconnect:', error);
      this.updateStatus('error', undefined, `Disconnect error: ${error}`);
    }
  }

  /**
   * Get current connection status
   */
  getStatus(): StreamingStatus {
    if (!this.socket) {
      return { state: 'disconnected' };
    }

    if (this.socket.connected) {
      if (this.isStreaming) {
        return {
          state: 'streaming',
          duration: this.startTime ? (Date.now() - this.startTime) / 1000 : 0
        };
      } else {
        return { state: 'connected' };
      }
    } else {
      return { state: 'connecting' };
    }
  }

  /**
   * Check if currently streaming
   */
  isCurrentlyStreaming(): boolean {
    return this.isStreaming && this.socket?.connected === true;
  }

  /**
   * Convert ArrayBuffer to base64
   */
  private arrayBufferToBase64(buffer: Uint8Array): string {
    let binary = '';
    const len = buffer.byteLength;
    for (let i = 0; i < len; i++) {
      binary += String.fromCharCode(buffer[i]);
    }
    return btoa(binary);
  }
}

/**
 * Utility function to create a streaming ASR client
 */
export function createStreamingASRClient(
  statusCallback?: StreamingStatusCallback,
  resultCallback?: StreamingResultCallback
): StreamingASRClient {
  return new StreamingASRClient(statusCallback, resultCallback);
}
