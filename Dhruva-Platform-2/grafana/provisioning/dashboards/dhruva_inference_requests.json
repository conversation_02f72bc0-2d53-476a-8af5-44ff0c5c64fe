{"annotations": {"list": [{"$$hashKey": "object:13", "builtIn": 1, "datasource": {"type": "datasource", "uid": "grafana"}, "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "target": {"limit": 100, "matchAny": false, "tags": [], "type": "dashboard"}, "type": "dashboard"}]}, "description": "", "editable": true, "fiscalYearStartMonth": 0, "graphTooltip": 1, "id": 3, "links": [], "liveNow": false, "panels": [{"datasource": {"type": "prometheus", "uid": "kdjfS_R7c"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "reqps"}, "overrides": []}, "gridPos": {"h": 5, "w": 12, "x": 0, "y": 0}, "id": 53, "options": {"orientation": "auto", "reduceOptions": {"calcs": ["mean"], "fields": "", "values": false}, "showThresholdLabels": false, "showThresholdMarkers": true, "text": {}}, "pluginVersion": "9.4.3", "targets": [{"datasource": {"type": "prometheus", "uid": "kdjfS_R7c"}, "editorMode": "code", "exemplar": true, "expr": "sum(rate(dhruva_inference_request_total{inference_service=~\"$inferenceServiceId\", user_id=~\"$userId\", api_key_name=~\"$apiKeyName\", task_type=~\"$taskType\", source_language=~\"$sourceLanguage\", target_language=~\"$targetLanguage\"}[5m])) OR vector(0)", "interval": "", "legendFormat": "", "range": true, "refId": "A"}], "title": "Requests Per Second", "type": "gauge"}, {"datasource": {"type": "prometheus", "uid": "kdjfS_R7c"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "s"}, "overrides": []}, "gridPos": {"h": 5, "w": 12, "x": 12, "y": 0}, "id": 58, "options": {"orientation": "auto", "reduceOptions": {"calcs": ["mean"], "fields": "", "values": false}, "showThresholdLabels": false, "showThresholdMarkers": true, "text": {}}, "pluginVersion": "9.4.3", "targets": [{"datasource": {"type": "prometheus", "uid": "kdjfS_R7c"}, "editorMode": "code", "expr": "histogram_quantile(0.5, sum(rate(dhruva_inference_request_duration_seconds_bucket{inference_service=~\"$inferenceServiceId\", user_id=~\"$userId\", api_key_name=~\"$apiKeyName\", task_type=~\"$taskType\", source_language=~\"$sourceLanguage\", target_language=~\"$targetLanguage\"}[5m]) > 0) by (le)) or vector(0)", "interval": "", "legendFormat": "", "range": true, "refId": "B"}], "title": "Request Processing Time", "type": "gauge"}, {"datasource": {"type": "prometheus", "uid": "kdjfS_R7c"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "links": [], "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "none"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 5}, "id": 59, "options": {"colorMode": "value", "graphMode": "area", "justifyMode": "auto", "orientation": "auto", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "textMode": "auto"}, "pluginVersion": "9.4.3", "targets": [{"datasource": {"type": "prometheus", "uid": "kdjfS_R7c"}, "editorMode": "code", "exemplar": true, "expr": "sum(increase(dhruva_inference_request_total{inference_service=~\"$inferenceServiceId\", user_id=~\"$userId\", api_key_name=~\"$apiKeyName\", task_type=~\"$taskType\", source_language=~\"$sourceLanguage\", target_language=~\"$targetLanguage\"}[$__range])) OR vector(0)", "interval": "", "legendFormat": "Rate", "range": true, "refId": "A"}], "title": "Requests Count", "type": "stat"}, {"datasource": {"type": "prometheus", "uid": "kdjfS_R7c"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "links": [], "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "reqps"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 5}, "id": 36, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "right", "showLegend": false}, "tooltip": {"mode": "multi", "sort": "none"}}, "pluginVersion": "9.4.3", "targets": [{"datasource": {"type": "prometheus", "uid": "kdjfS_R7c"}, "editorMode": "code", "exemplar": true, "expr": "sum(rate(dhruva_inference_request_total{inference_service=~\"$inferenceServiceId\", user_id=~\"$userId\", api_key_name=~\"$apiKeyName\", task_type=~\"$taskType\", source_language=~\"$sourceLanguage\", target_language=~\"$targetLanguage\"}[5m])) OR vector(0)", "interval": "", "legendFormat": "Rate", "range": true, "refId": "A"}], "title": "Requests Per Second", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "kdjfS_R7c"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "links": [], "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "s"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 13}, "id": 40, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "none"}}, "pluginVersion": "9.4.3", "targets": [{"datasource": {"type": "prometheus", "uid": "kdjfS_R7c"}, "editorMode": "code", "exemplar": true, "expr": "histogram_quantile(0.95, sum(rate(dhruva_inference_request_duration_seconds_bucket{inference_service=~\"$inferenceServiceId\", user_id=~\"$userId\", api_key_name=~\"$apiKeyName\", task_type=~\"$taskType\", source_language=~\"$sourceLanguage\", target_language=~\"$targetLanguage\"}[5m])) by (le)) OR vector(0)", "interval": "", "legendFormat": "0.95", "range": true, "refId": "A"}, {"datasource": {"type": "prometheus", "uid": "kdjfS_R7c"}, "editorMode": "code", "exemplar": true, "expr": "histogram_quantile(0.9, sum(rate(dhruva_inference_request_duration_seconds_bucket{inference_service=~\"$inferenceServiceId\", user_id=~\"$userId\", api_key_name=~\"$apiKeyName\", task_type=~\"$taskType\", source_language=~\"$sourceLanguage\", target_language=~\"$targetLanguage\"}[5m])) by (le)) OR vector(0)", "hide": false, "interval": "", "legendFormat": "0.9", "range": true, "refId": "B"}, {"datasource": {"type": "prometheus", "uid": "kdjfS_R7c"}, "editorMode": "code", "exemplar": true, "expr": "histogram_quantile(0.5, sum(rate(dhruva_inference_request_duration_seconds_bucket{inference_service=~\"$inferenceServiceId\", user_id=~\"$userId\", api_key_name=~\"$apiKeyName\", task_type=~\"$taskType\", source_language=~\"$sourceLanguage\", target_language=~\"$targetLanguage\"}[5m])) by (le)) OR vector(0)", "hide": false, "interval": "", "legendFormat": "0.5", "range": true, "refId": "C"}, {"datasource": {"type": "prometheus", "uid": "kdjfS_R7c"}, "editorMode": "code", "exemplar": true, "expr": "histogram_quantile(0.1, sum(rate(dhruva_inference_request_duration_seconds_bucket{inference_service=~\"$inferenceServiceId\", user_id=~\"$userId\", api_key_name=~\"$apiKeyName\", task_type=~\"$taskType\", source_language=~\"$sourceLanguage\", target_language=~\"$targetLanguage\"}[5m])) by (le)) OR vector(0)", "hide": false, "interval": "", "legendFormat": "0.1", "range": true, "refId": "D"}], "title": "Requests Processing Time", "type": "timeseries"}], "refresh": "", "revision": 1, "schemaVersion": 38, "style": "dark", "tags": [], "templating": {"list": [{"current": {"selected": false, "text": ".*", "value": ".*"}, "description": "", "hide": 0, "label": "API Key Name", "name": "apiKeyName", "options": [{"selected": true, "text": ".*", "value": ".*"}], "query": ".*", "skipUrlSync": false, "type": "textbox"}, {"current": {"selected": false, "text": ".*", "value": ".*"}, "description": "", "hide": 0, "label": "User Id", "name": "userId", "options": [{"selected": true, "text": ".*", "value": ".*"}], "query": ".*", "skipUrlSync": false, "type": "textbox"}, {"current": {"selected": false, "text": ".*", "value": ".*"}, "hide": 0, "label": "Inference Service Id", "name": "inferenceServiceId", "options": [{"selected": true, "text": ".*", "value": ".*"}], "query": ".*", "skipUrlSync": false, "type": "textbox"}, {"current": {"selected": false, "text": ".*", "value": ".*"}, "hide": 0, "label": "Task Type", "name": "taskType", "options": [{"selected": true, "text": ".*", "value": ".*"}], "query": ".*", "skipUrlSync": false, "type": "textbox"}, {"current": {"selected": false, "text": ".*", "value": ".*"}, "hide": 0, "label": "Source Language", "name": "sourceLanguage", "options": [{"selected": true, "text": ".*", "value": ".*"}], "query": ".*", "skipUrlSync": false, "type": "textbox"}, {"current": {"selected": false, "text": ".*", "value": ".*"}, "hide": 0, "label": "Target Language", "name": "targetLanguage", "options": [{"selected": true, "text": ".*", "value": ".*"}], "query": ".*", "skipUrlSync": false, "type": "textbox"}]}, "time": {"from": "now-1h", "to": "now"}, "timepicker": {"refresh_intervals": ["5s", "10s", "30s", "1m", "5m", "15m", "30m", "1h", "2h", "1d"]}, "timezone": "", "title": "Dhruva Inference Request Dashboard", "uid": "Ye6zPeA7y", "version": 3, "weekStart": ""}