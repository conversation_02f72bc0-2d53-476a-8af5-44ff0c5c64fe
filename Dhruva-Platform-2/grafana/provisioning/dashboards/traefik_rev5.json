{"__inputs": [{"name": "DS_PROMETHEUS", "label": "Prometheus", "description": "", "type": "datasource", "pluginId": "prometheus", "pluginName": "Prometheus"}], "__requires": [{"type": "grafana", "id": "grafana", "name": "<PERSON><PERSON>", "version": "7.5.5"}, {"type": "panel", "id": "graph", "name": "Graph", "version": ""}, {"type": "panel", "id": "piechart", "name": "Pie chart v2", "version": ""}, {"type": "datasource", "id": "prometheus", "name": "Prometheus", "version": "1.0.0"}, {"type": "panel", "id": "singlestat", "name": "Singlestat", "version": ""}], "annotations": {"list": [{"builtIn": 1, "datasource": "-- <PERSON><PERSON> --", "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "type": "dashboard"}]}, "description": "Traefik dashboard prometheus", "editable": true, "gnetId": 4475, "graphTooltip": 0, "id": null, "iteration": 1620932097756, "links": [], "panels": [{"datasource": null, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 0}, "id": 10, "title": "$backend stats", "type": "row"}, {"cacheTimeout": null, "datasource": {"type": "prometheus", "uid": "kdjfS_R7c"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "decimals": 0, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 7, "w": 12, "x": 0, "y": 1}, "id": 2, "interval": null, "links": [], "maxDataPoints": 3, "options": {"displayLabels": [], "legend": {"calcs": [], "displayMode": "table", "placement": "right", "values": ["value", "percent"]}, "pieType": "pie", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "text": {}}, "targets": [{"exemplar": true, "expr": "traefik_service_requests_total{service=\"$service\"}", "format": "time_series", "interval": "", "intervalFactor": 2, "legendFormat": "{{method}} : {{code}}", "refId": "A"}], "title": "$service return code", "type": "piechart"}, {"cacheTimeout": null, "colorBackground": false, "colorValue": false, "colors": ["#299c46", "rgba(237, 129, 40, 0.89)", "#d44a3a"], "datasource": {"type": "prometheus", "uid": "kdjfS_R7c"}, "fieldConfig": {"defaults": {}, "overrides": []}, "format": "ms", "gauge": {"maxValue": 100, "minValue": 0, "show": false, "thresholdLabels": false, "thresholdMarkers": true}, "gridPos": {"h": 7, "w": 12, "x": 12, "y": 1}, "id": 4, "interval": null, "links": [], "mappingType": 1, "mappingTypes": [{"name": "value to text", "value": 1}, {"name": "range to text", "value": 2}], "maxDataPoints": 100, "nullPointMode": "connected", "nullText": null, "postfix": "", "postfixFontSize": "50%", "prefix": "", "prefixFontSize": "50%", "rangeMaps": [{"from": "null", "text": "N/A", "to": "null"}], "sparkline": {"fillColor": "rgba(31, 118, 189, 0.18)", "full": false, "lineColor": "rgb(31, 120, 193)", "show": true}, "tableColumn": "", "targets": [{"exemplar": true, "expr": "sum(traefik_service_request_duration_seconds_sum{service=\"$service\"}) / sum(traefik_service_requests_total{service=\"$service\"}) * 1000", "format": "time_series", "interval": "", "intervalFactor": 2, "legendFormat": "", "refId": "A"}], "thresholds": "", "title": "$service response time", "type": "singlestat", "valueFontSize": "80%", "valueMaps": [{"op": "=", "text": "N/A", "value": "null"}], "valueName": "avg"}, {"aliasColors": {}, "bars": true, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus", "uid": "kdjfS_R7c"}, "fieldConfig": {"defaults": {}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 7, "w": 24, "x": 0, "y": 8}, "hiddenSeries": false, "id": 3, "legend": {"alignAsTable": true, "avg": true, "current": false, "max": true, "min": true, "rightSide": true, "show": true, "total": false, "values": true}, "lines": false, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.5", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "sum(rate(traefik_service_requests_total{service=\"$service\"}[5m]))", "format": "time_series", "interval": "", "intervalFactor": 2, "legendFormat": "Total requests $service", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Total requests over 5min $service", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"collapsed": false, "datasource": null, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 15}, "id": 12, "panels": [], "title": "Global stats", "type": "row"}, {"aliasColors": {}, "bars": true, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus", "uid": "kdjfS_R7c"}, "fieldConfig": {"defaults": {}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 7, "w": 12, "x": 0, "y": 16}, "hiddenSeries": false, "id": 5, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": true, "min": true, "rightSide": true, "show": true, "total": false, "values": true}, "lines": false, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.5", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": true, "steppedLine": false, "targets": [{"expr": "rate(traefik_entrypoint_requests_total{entrypoint=~\"$entrypoint\",code=\"200\"}[5m])", "format": "time_series", "intervalFactor": 2, "legendFormat": "{{method}} : {{code}}", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Status code 200 over 5min", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": true, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus", "uid": "kdjfS_R7c"}, "fieldConfig": {"defaults": {}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 7, "w": 12, "x": 12, "y": 16}, "hiddenSeries": false, "id": 6, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": true, "min": true, "rightSide": true, "show": true, "total": false, "values": true}, "lines": false, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.5", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": true, "steppedLine": false, "targets": [{"expr": "rate(traefik_entrypoint_requests_total{entrypoint=~\"$entrypoint\",code!=\"200\"}[5m])", "format": "time_series", "intervalFactor": 2, "legendFormat": "{{ method }} : {{code}}", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Others status code over 5min", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"cacheTimeout": null, "datasource": {"type": "prometheus", "uid": "kdjfS_R7c"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "decimals": 0, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 7, "w": 12, "x": 0, "y": 23}, "id": 7, "interval": null, "links": [], "maxDataPoints": 3, "options": {"displayLabels": [], "legend": {"calcs": [], "displayMode": "table", "placement": "right", "values": ["value"]}, "pieType": "pie", "reduceOptions": {"calcs": ["sum"], "fields": "", "values": false}, "text": {}}, "targets": [{"exemplar": true, "expr": "sum(rate(traefik_service_requests_total[5m])) by (service) ", "format": "time_series", "interval": "", "intervalFactor": 2, "legendFormat": "{{ service }}", "refId": "A"}], "title": "Requests by service", "type": "piechart"}, {"cacheTimeout": null, "datasource": {"type": "prometheus", "uid": "kdjfS_R7c"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "decimals": 0, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 7, "w": 12, "x": 12, "y": 23}, "id": 8, "interval": null, "links": [], "maxDataPoints": 3, "options": {"displayLabels": [], "legend": {"calcs": [], "displayMode": "table", "placement": "right", "values": ["value"]}, "pieType": "pie", "reduceOptions": {"calcs": ["sum"], "fields": "", "values": false}, "text": {}}, "targets": [{"exemplar": true, "expr": "sum(rate(traefik_entrypoint_requests_total{entrypoint =~ \"$entrypoint\"}[5m])) by (entrypoint) ", "format": "time_series", "interval": "", "intervalFactor": 2, "legendFormat": "{{ entrypoint }}", "refId": "A"}], "title": "Requests by protocol", "type": "piechart"}], "schemaVersion": 27, "style": "dark", "tags": ["traefik", "prometheus"], "templating": {"list": [{"allValue": null, "current": {}, "datasource": {"type": "prometheus", "uid": "kdjfS_R7c"}, "definition": "label_values(service)", "description": null, "error": null, "hide": 0, "includeAll": false, "label": null, "multi": false, "name": "service", "options": [], "query": {"query": "label_values(service)", "refId": "StandardVariableQuery"}, "refresh": 1, "regex": "", "skipUrlSync": false, "sort": 0, "tagValuesQuery": "", "tags": [], "tagsQuery": "", "type": "query", "useTags": false}, {"allValue": null, "current": {}, "datasource": {"type": "prometheus", "uid": "kdjfS_R7c"}, "definition": "", "description": null, "error": null, "hide": 0, "includeAll": true, "label": null, "multi": true, "name": "entrypoint", "options": [], "query": {"query": "label_values(entrypoint)", "refId": "Prometheus-entrypoint-Variable-Query"}, "refresh": 1, "regex": "", "skipUrlSync": false, "sort": 0, "tagValuesQuery": "", "tags": [], "tagsQuery": "", "type": "query", "useTags": false}]}, "time": {"from": "now-1h", "to": "now"}, "timepicker": {"refresh_intervals": ["5s", "10s", "30s", "1m", "5m", "15m", "30m", "1h", "2h", "1d"], "time_options": ["5m", "15m", "1h", "6h", "12h", "24h", "2d", "7d", "30d"]}, "timezone": "", "title": "Traefik", "uid": "qPdAviJmz", "version": 10}