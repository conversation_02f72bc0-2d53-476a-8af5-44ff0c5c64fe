{"name": "client", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "export": "next export"}, "dependencies": {"@ai4bharat/indic-transliterate": "^1.2.15", "@chakra-ui/icons": "^2.0.17", "@chakra-ui/react": "^2.4.6", "@emotion/react": "^11.10.5", "@emotion/styled": "^11.10.5", "@next/font": "13.1.1", "@project-sunbird/open-speech-streaming-client": "https://github.com/AI4Bharat/speech-recognition-open-api-client.git#dhruva", "@tanstack/react-query": "^4.24.6", "@tanstack/react-query-devtools": "^4.24.6", "@types/node": "18.11.18", "@types/react": "18.0.26", "@types/react-dom": "18.0.10", "axios": "^1.2.2", "dotenv": "^16.0.3", "eslint": "8.30.0", "eslint-config-next": "13.1.1", "framer-motion": "^8.1.4", "next": "13.1.1", "react": "18.2.0", "react-datepicker": "^4.14.1", "react-dom": "18.2.0", "react-icons": "^4.7.1", "typescript": "4.9.4"}}