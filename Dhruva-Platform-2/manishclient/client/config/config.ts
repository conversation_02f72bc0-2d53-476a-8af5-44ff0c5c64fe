const lang2label: { [key: string]: string } = {
  as : "Assamese",
  awa: "Awadhi",
  bho: "Bhojpuri",
  bn : "<PERSON><PERSON>",
  brx: "<PERSON><PERSON>",
  doi: "Dogri",
  en : "English",
  gom: "Goan-Konkani",
  gu : "Gujarati",
  hi : "Hindi",
  hne: "Hindi-Eastern (Chhattisgarhi)",
  kn : "Kannada",
  ks : "Kashmiri",
  ks_Deva: "Kashmiri (Devanagari)",
  kha: "Khasi",
  lus: "<PERSON>shai (Mizo)",
  mag: "Magahi",
  mai: "<PERSON>thi<PERSON>",
  ml : "Malayalam",
  mni: "Manipuri",
  mni_Beng: "Manipuri (Bengali)",
  mr : "Marathi",
  ne : "Nepali",
  or : "Oriya",
  pa : "Panjabi",
  raj: "Rajasthani",
  sa : "Sanskrit",
  sat: "Santali",
  sd : "Sindhi",
  sd_Deva: "Sindhi (Devanagari)",
  si : "Sinhala",
  ta : "Tamil",
  te : "Telugu",
  ur : "Urdu",
};
const dhruvaRootURL: string = "http://*************:8000";

const dhruvaConfig: { [key: string]: string } = {
  listServices: `${dhruvaRootURL}/services/details/list_services`,
  viewService: `${dhruvaRootURL}/services/details/view_service`,
  listModels: `${dhruvaRootURL}/services/details/list_models`,
  viewModel: `${dhruvaRootURL}/services/details/view_model`,
  genericInference: `${dhruvaRootURL}/services/inference`,
  translationInference: `${dhruvaRootURL}/services/inference/translation`,
  ttsInference: `${dhruvaRootURL}/services/inference/tts`,
  asrInference: `${dhruvaRootURL}/services/inference/asr`,
  asrStreamingInference: `wss://api.dhruva.ai4bharat.org`,
  stsInference: `${dhruvaRootURL}/services/inference/s2s`,
  nerInference: `${dhruvaRootURL}/services/inference/ner`,
};

const tag2Color = {
  "B-LOC": ["#ffcccc", "#ff0000"],
  "B-ORG": ["#cceeff", "#00aaff"],
  "B-PER": ["#d6f5d6", "#33cc33"],
  "I-LOC": ["#ffccdd", "#ff0055"],
  "I-ORG": ["#ffffcc", "#ffff00"],
  "I-PER": ["#e6ccff", "#8000ff"],
  O: ["#ffe6cc", "#ff8000"],
};

export { lang2label, tag2Color,dhruvaConfig };
