# Git files
.git
.gitignore
.gitattributes

# Documentation
*.md
README*
CHANGELOG*
LICENSE*
docs/
documentation/

# IDE and editor files
.vscode/
.idea/
*.swp
*.swo
*~
.DS_Store
Thumbs.db

# Python cache and virtual environments
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST
.env.local
.env.development
.env.test
.env.production
venv/
env/
ENV/

# Testing
.pytest_cache/
.coverage
htmlcov/
.tox/
.nox/
.cache
nosetests.xml
coverage.xml
*.cover
.hypothesis/

# Logs
*.log
logs/

# Temporary files
*.tmp
*.temp
temp/
tmp/

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Node modules (if any)
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Database files
*.db
*.sqlite
*.sqlite3

# Backup files
*.bak
*.backup
*~

# Large files that shouldn't be in Docker context
*.tar
*.tar.gz
*.zip
*.rar
*.7z

# OAuth and email related files (removed)
oauth_*
email_*
risc_*
