{"dashboard": {"annotations": {"list": [{"$$hashKey": "object:13", "builtIn": 1, "datasource": {"type": "datasource", "uid": "grafana"}, "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "target": {"limit": 100, "matchAny": false, "tags": [], "type": "dashboard"}, "type": "dashboard"}]}, "description": "", "editable": true, "fiscalYearStartMonth": 0, "graphTooltip": 1, "id": 13, "links": [], "liveNow": false, "panels": [{"datasource": {"type": "prometheus", "uid": "vowwS_B4k"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "reqps"}, "overrides": []}, "gridPos": {"h": 5, "w": 12, "x": 0, "y": 0}, "id": 53, "options": {"orientation": "auto", "reduceOptions": {"calcs": ["mean"], "fields": "", "values": false}, "showThresholdLabels": false, "showThresholdMarkers": true, "text": {}}, "pluginVersion": "9.4.3", "targets": [{"datasource": {"type": "prometheus", "uid": "vowwS_B4k"}, "editorMode": "code", "exemplar": true, "expr": "sum(rate(dhruva_requests_total{inference_service=\"$inferenceServiceId\", user_id=\"$userId\", api_key_name=\"$apiKeyName\"}[5m])) OR vector(0)", "interval": "", "legendFormat": "", "range": true, "refId": "A"}], "title": "Requests Per Second", "type": "gauge"}, {"datasource": {"type": "prometheus", "uid": "vowwS_B4k"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "ms"}, "overrides": []}, "gridPos": {"h": 5, "w": 12, "x": 12, "y": 0}, "id": 58, "options": {"orientation": "auto", "reduceOptions": {"calcs": ["mean"], "fields": "", "values": false}, "showThresholdLabels": false, "showThresholdMarkers": true, "text": {}}, "pluginVersion": "9.4.3", "targets": [{"datasource": {"type": "prometheus", "uid": "vowwS_B4k"}, "editorMode": "code", "expr": "histogram_quantile(0.5, sum(rate(dhruva_request_duration_seconds_bucket{inference_service=\"$inferenceServiceId\", user_id=\"$userId\", api_key_name=\"$apiKeyName\"}[5m])) by (le)) * 1000 OR vector(0)", "interval": "", "legendFormat": "", "range": true, "refId": "B"}], "title": "Request Processing Time", "type": "gauge"}, {"datasource": {"type": "prometheus", "uid": "vowwS_B4k"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "links": [], "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "reqps"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 5}, "id": 36, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "right", "showLegend": false}, "tooltip": {"mode": "multi", "sort": "none"}}, "pluginVersion": "9.4.3", "targets": [{"datasource": {"type": "prometheus", "uid": "vowwS_B4k"}, "editorMode": "code", "exemplar": true, "expr": "sum(rate(dhruva_requests_total{inference_service=\"$inferenceServiceId\", user_id=\"$userId\", api_key_name=\"$apiKeyName\"}[5m])) OR vector(0)", "interval": "", "legendFormat": "Rate", "range": true, "refId": "A"}], "title": "Requests Per Second", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "vowwS_B4k"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "links": [], "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "s"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 5}, "id": 40, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "none"}}, "pluginVersion": "9.4.3", "targets": [{"datasource": {"type": "prometheus", "uid": "vowwS_B4k"}, "editorMode": "code", "exemplar": true, "expr": "histogram_quantile(0.95, sum(rate(dhruva_request_duration_seconds_bucket{inference_service=\"$inferenceServiceId\", user_id=\"$userId\", api_key_name=\"$apiKeyName\"}[5m])) by (le)) OR vector(0)", "interval": "", "legendFormat": "0.95", "range": true, "refId": "A"}, {"datasource": {"type": "prometheus", "uid": "vowwS_B4k"}, "editorMode": "code", "exemplar": true, "expr": "histogram_quantile(0.9, sum(rate(dhruva_request_duration_seconds_bucket{inference_service=\"$inferenceServiceId\", user_id=\"$userId\", api_key_name=\"$apiKeyName\"}[5m])) by (le)) OR vector(0)", "hide": false, "interval": "", "legendFormat": "0.9", "range": true, "refId": "B"}, {"datasource": {"type": "prometheus", "uid": "vowwS_B4k"}, "editorMode": "code", "exemplar": true, "expr": "histogram_quantile(0.5, sum(rate(dhruva_request_duration_seconds_bucket{inference_service=\"$inferenceServiceId\", user_id=\"$userId\", api_key_name=\"$apiKeyName\"}[5m])) by (le)) OR vector(0)", "hide": false, "interval": "", "legendFormat": "0.5", "range": true, "refId": "C"}, {"datasource": {"type": "prometheus", "uid": "vowwS_B4k"}, "editorMode": "code", "exemplar": true, "expr": "histogram_quantile(0.1, sum(rate(dhruva_request_duration_seconds_bucket{inference_service=\"$inferenceServiceId\", user_id=\"$userId\", api_key_name=\"$apiKeyName\"}[5m])) by (le)) OR vector(0)", "hide": false, "interval": "", "legendFormat": "0.1", "range": true, "refId": "D"}], "title": "Requests Processing Time", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "vowwS_B4k"}, "description": "", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 30, "gradientMode": "opacity", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "links": [], "mappings": [], "max": 100, "min": 0, "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "percent"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 13}, "id": 32, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "none"}}, "pluginVersion": "9.4.3", "targets": [{"datasource": {"type": "prometheus", "uid": "vowwS_B4k"}, "editorMode": "code", "exemplar": true, "expr": "sum(rate(dhruva_requests_total{status_code!~\"2..\", inference_service=\"$inferenceServiceId\", user_id=\"$userId\", api_key_name=\"$apiKeyName\"}[5m])) * 100 / sum(rate(dhruva_requests_total[5m])) OR vector(0)", "interval": "", "legendFormat": "Error Rate", "range": true, "refId": "A"}], "title": "Error Rate", "type": "timeseries"}], "refresh": "", "revision": 1, "schemaVersion": 38, "style": "dark", "tags": [], "templating": {"list": [{"current": {"selected": false, "text": "$API_KEY_NAME", "value": "$API_KEY_NAME"}, "datasource": {"type": "datasource", "uid": "-- Dashboard --"}, "definition": "[object Object]", "error": {}, "hide": 0, "includeAll": false, "label": "API Key Name", "multi": false, "name": "apiKeyName", "options": [{"selected": false, "text": "$API_KEY_NAME", "value": "$API_KEY_NAME"}], "query": "", "refresh": 0, "regex": "", "skipUrlSync": false, "sort": 0, "tagValuesQuery": "", "tagsQuery": "", "type": "query", "useTags": false}, {"current": {"selected": false, "text": "$USER_ID", "value": "$USER_ID"}, "datasource": {"type": "datasource", "uid": "-- Dashboard --"}, "definition": "[object Object]", "error": {}, "hide": 0, "includeAll": false, "label": "User Id", "multi": false, "name": "userId", "options": [{"selected": false, "text": "$USER_ID", "value": "$USER_ID"}], "query": "", "refresh": 0, "regex": "", "skipUrlSync": false, "sort": 0, "tagValuesQuery": "", "tagsQuery": "", "type": "query", "useTags": false}, {"current": {"selected": false, "text": "$INFERENCE_SERVICE_ID", "value": "$INFERENCE_SERVICE_ID"}, "datasource": {"type": "datasource", "uid": "-- Dashboard --"}, "definition": "[object Object]", "error": {}, "hide": 0, "includeAll": false, "label": "Inference Service Id", "multi": false, "name": "inferenceServiceId", "options": [{"selected": false, "text": "$INFERENCE_SERVICE_ID", "value": "$INFERENCE_SERVICE_ID"}], "query": "", "refresh": 0, "regex": "", "skipUrlSync": false, "sort": 0, "tagValuesQuery": "", "tagsQuery": "", "type": "query", "useTags": false}]}, "timepicker": {"refresh_intervals": ["5s", "10s", "30s", "1m", "5m", "15m", "30m", "1h", "2h", "1d"]}, "timezone": "", "title": "Dhruva Service Specific Dashboard", "uid": "Zj4zOgA7y", "version": 2, "weekStart": ""}}